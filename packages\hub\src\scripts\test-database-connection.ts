#!/usr/bin/env tsx

/**
 * Test script to check database connections and model availability
 * Run with: npx tsx src/scripts/test-database-connection.ts
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

// Import after env loading
import { getSupabaseClient } from '../lib/supabase';
import { usageTrackingService } from '../services/usage-tracking.service';
import { modelSelectorService } from '../services/model-selector.service';
import { redisConnectionPool } from '../services/redis-connection-pool.service';

async function testDatabaseConnection() {
  console.log('🧪 Testing database connections and model availability...');
  
  // Test environment variables
  console.log('\n📋 Environment Variables:');
  const envVars = {
    SUPABASE_URL: process.env.SUPABASE_URL ? `Set (${process.env.SUPABASE_URL.length} chars)` : 'Missing',
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? `Set (${process.env.SUPABASE_SERVICE_ROLE_KEY.length} chars)` : 'Missing',
    UPSTASH_REDIS_URL: process.env.UPSTASH_REDIS_URL ? `Set (${process.env.UPSTASH_REDIS_URL.length} chars)` : 'Missing',
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY ? `Set (${process.env.OPENROUTER_API_KEY.length} chars)` : 'Missing',
    GOOGLE_GEMINI_API_KEY: process.env.GOOGLE_GEMINI_API_KEY ? `Set (${process.env.GOOGLE_GEMINI_API_KEY.length} chars)` : 'Missing',
  };
  
  Object.entries(envVars).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  
  // Test Supabase connection
  console.log('\n🗄️  Testing Supabase connection...');
  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('ai_model_configs')
      .select('id, is_active, daily_request_limit')
      .limit(5);
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
    } else {
      console.log(`✅ Supabase connected successfully`);
      console.log(`📊 Found ${data.length} AI model configs`);
      data.forEach(config => {
        console.log(`  - ${config.id}: active=${config.is_active}, limit=${config.daily_request_limit}`);
      });
    }
  } catch (error) {
    console.error('❌ Supabase connection error:', error);
  }
  
  // Test Redis connection
  console.log('\n🔴 Testing Redis connection...');
  try {
    await redisConnectionPool.execute(async (redis) => {
      const testKey = 'test:connection';
      const testValue = 'test-value';
      
      await redis.set(testKey, testValue, { ex: 10 });
      const retrieved = await redis.get(testKey);
      
      if (retrieved === testValue) {
        console.log('✅ Redis connected successfully');
      } else {
        console.error('❌ Redis test failed - value mismatch');
      }
      
      await redis.del(testKey);
    });
  } catch (error) {
    console.error('❌ Redis connection error:', error);
  }
  
  // Test model availability
  console.log('\n🤖 Testing model availability...');
  const models = [
    'moonshotai/kimi-k2:free',
    'google/gemini-2.5-flash',
    'google/gemini-2.0-flash',
    'google/gemini-2.5-pro',
    'openai/gpt-4.1-nano'
  ];
  
  for (const modelId of models) {
    try {
      const isAvailable = await usageTrackingService.isModelAvailable(modelId);
      const usage = await usageTrackingService.getCurrentUsage(modelId);
      
      console.log(`  ${modelId}:`);
      console.log(`    Available: ${isAvailable ? '✅' : '❌'}`);
      console.log(`    Requests: ${usage.requestCount}`);
      console.log(`    Limits: ${JSON.stringify(usage.dailyLimits)}`);
    } catch (error) {
      console.error(`  ${modelId}: ❌ Error - ${error}`);
    }
  }
  
  // Test model selection
  console.log('\n🎯 Testing model selection...');
  try {
    const testContent = 'Day 1: Visit London Tower. Day 2: See Big Ben.';
    const selection = await modelSelectorService.selectModel(testContent);
    
    console.log('✅ Model selection successful:');
    console.log(`  Selected: ${selection.modelId}`);
    console.log(`  Provider: ${selection.provider}`);
    console.log(`  Reason: ${selection.reason}`);
    console.log(`  Cost: $${selection.estimatedCost}`);
    console.log(`  Fallbacks: ${selection.fallbackChain.join(', ')}`);
  } catch (error) {
    console.error('❌ Model selection failed:', error);
  }
  
  // Test token estimation
  console.log('\n📊 Testing token estimation...');
  try {
    const testContent = `
Day 1: London
- Morning: Arrive at Heathrow Airport
- Afternoon: Check into hotel near Tower Bridge
- Evening: Dinner at Borough Market

Day 2: London  
- Morning: Visit Tower of London
- Afternoon: Walk across Tower Bridge
- Evening: Thames river cruise
    `;
    
    const estimate = modelSelectorService.estimateTokens(testContent);
    console.log('✅ Token estimation successful:');
    console.log(`  Input tokens: ${estimate.inputTokens}`);
    console.log(`  Output tokens: ${estimate.outputTokens}`);
    console.log(`  Complexity: ${estimate.complexity}`);
  } catch (error) {
    console.error('❌ Token estimation failed:', error);
  }
  
  console.log('\n🏁 Database connection test completed');
}

// Run the test
testDatabaseConnection().catch(console.error);
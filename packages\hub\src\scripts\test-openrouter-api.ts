#!/usr/bin/env tsx

/**
 * Test script to isolate OpenRouter API issues
 * Run with: npx tsx src/scripts/test-openrouter-api.ts
 */

import axios from 'axios';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

async function testOpenRouterAPI() {
  console.log('🧪 Testing OpenRouter API directly...');
  
  const apiKey = process.env.OPENROUTER_API_KEY;
  if (!apiKey) {
    console.error('❌ OPENROUTER_API_KEY not found in environment');
    process.exit(1);
  }
  
  console.log(`✅ API Key found (length: ${apiKey.length})`);
  console.log(`🔑 Key prefix: ${apiKey.substring(0, 10)}...`);
  
  const testContent = `
Day 1: London
- Morning: Arrive at Heathrow Airport
- Afternoon: Check into hotel near Tower Bridge
- Evening: Dinner at Borough Market

Day 2: London
- Morning: Visit Tower of London
- Afternoon: Walk across Tower Bridge
- Evening: Thames river cruise
  `;

  const systemPrompt = `You are a travel itinerary parser. Parse the following travel content into a structured JSON format.

Return a JSON object with this exact structure:
{
  "title": "Trip title",
  "destination": "Main destination",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD", 
  "activities": [
    {
      "title": "Activity name",
      "description": "Activity description",
      "date": "YYYY-MM-DD",
      "time": "HH:MM",
      "location": {
        "address": "Location address"
      },
      "type": "sightseeing"
    }
  ],
  "metadata": {
    "source": "unknown",
    "confidence": 0.8,
    "warnings": [],
    "parseDate": "${new Date().toISOString()}",
    "version": "1.0"
  }
}`;

  try {
    console.log('📤 Making request to OpenRouter API...');
    console.log(`📊 Content length: ${testContent.length} characters`);
    console.log(`📊 System prompt length: ${systemPrompt.length} characters`);
    
    const startTime = Date.now();
    
    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'moonshotai/kimi-k2:free',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: testContent
          }
        ],
        temperature: 0.3,
        max_tokens: 4000,
        response_format: { type: 'json_object' }
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://travelviz.app',
          'X-Title': 'TravelViz'
        },
        timeout: 60000 // 60 seconds
      }
    );
    
    const duration = Date.now() - startTime;
    
    console.log(`✅ Request completed in ${duration}ms`);
    console.log(`📊 Response status: ${response.status} ${response.statusText}`);
    console.log(`📊 Response headers:`, Object.keys(response.headers));
    
    if (response.data?.error) {
      console.error('❌ API returned error:', response.data.error);
      return;
    }
    
    const aiResponse = response.data.choices?.[0]?.message?.content;
    if (!aiResponse) {
      console.error('❌ No response content received');
      console.log('📊 Full response data:', JSON.stringify(response.data, null, 2));
      return;
    }
    
    console.log(`✅ Response received (${aiResponse.length} characters)`);
    console.log('📄 Response preview:', aiResponse.substring(0, 200) + '...');
    
    // Try to parse JSON
    try {
      const parsed = JSON.parse(aiResponse);
      console.log('✅ JSON parsing successful');
      console.log('📊 Parsed structure:', {
        hasTitle: !!parsed.title,
        hasDestination: !!parsed.destination,
        activitiesCount: parsed.activities?.length || 0,
        hasMetadata: !!parsed.metadata
      });
    } catch (parseError) {
      console.error('❌ JSON parsing failed:', parseError);
      console.log('📄 Raw response:', aiResponse);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ Request failed after ${duration}ms`);
    
    if (axios.isAxiosError(error)) {
      console.error('📊 Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        code: error.code
      });
      
      if (error.code === 'ECONNABORTED') {
        console.error('⏰ Request timed out');
      }
      
      if (error.response?.status === 429) {
        console.error('🚫 Rate limit exceeded');
      }
      
      if (error.response?.status === 401) {
        console.error('🔐 Authentication failed - check API key');
      }
    } else {
      console.error('❌ Unexpected error:', error);
    }
  }
}

// Run the test
testOpenRouterAPI().catch(console.error);
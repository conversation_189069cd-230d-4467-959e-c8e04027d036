import axios, { AxiosInstance, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { createErrorResponse, ApiResponse, Trip, Activity, Profile, ActivityType } from '@travelviz/shared';

// Types for our API
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  expiresAt: number;
}

// Auth API response interfaces
interface AuthApiResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at?: number;
}

// Transform auth API response to AuthTokens
function transformAuthResponse(apiResponse: AuthApiResponse): AuthTokens {
  return {
    accessToken: apiResponse.access_token,
    refreshToken: apiResponse.refresh_token,
    expiresIn: apiResponse.expires_in,
    expiresAt: apiResponse.expires_at || (Date.now() + apiResponse.expires_in * 1000),
  };
}

// Trip creation/update input types
interface TripCreateInput {
  title: string;
  description?: string;
  destination?: string;
  start_date?: string;
  end_date?: string;
  budget_amount?: number;
  budget_currency?: string;
  tags?: string[];
  visibility?: 'private' | 'unlisted' | 'public';
}

interface TripUpdateInput extends Partial<TripCreateInput> {
  status?: 'draft' | 'planning' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
}

// Activity creation/update input types
interface ActivityCreateInput {
  title: string;
  description?: string;
  type?: ActivityType;
  start_time?: string;
  end_time?: string;
  location?: string;
  location_lat?: number;
  location_lng?: number;
  price?: number;
  currency?: string;
  booking_reference?: string;
  booking_url?: string;
  notes?: string;
  attachments?: string[];
}

interface ActivityUpdateInput extends Partial<ActivityCreateInput> {
  position?: number;
}


// Places API response types
interface PlaceAutocompleteResult {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
  types: string[];
}

interface PlaceDetails {
  place_id: string;
  name: string;
  formatted_address: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
  }>;
  rating?: number;
  types: string[];
  website?: string;
  international_phone_number?: string;
}

// Create axios instance with base configuration
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_HUB_URL || 'http://localhost:3001',
  timeout: 90000, // Increased to 90 seconds for long-running AI operations
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Token management
let authTokens: AuthTokens | null = null;

// Load tokens from localStorage on initialization
if (typeof window !== 'undefined') {
  const storedTokens = localStorage.getItem('authTokens');
  if (storedTokens) {
    try {
      authTokens = JSON.parse(storedTokens);
      // Check if tokens are expired
      if (authTokens && authTokens.expiresAt < Date.now()) {
        authTokens = null;
        localStorage.removeItem('authTokens');
      }
    } catch (error) {
      // Failed to parse stored tokens - clearing invalid data
      localStorage.removeItem('authTokens');
    }
  }
}

// Request interceptor to add auth token
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    if (authTokens && authTokens.accessToken) {
      config.headers.Authorization = `Bearer ${authTokens.accessToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry && authTokens?.refreshToken) {
      originalRequest._retry = true;

      try {
        const response = await authAPI.refreshToken(authTokens.refreshToken);
        if (response.success && response.data) {
          setAuthTokens(response.data);
          originalRequest.headers.Authorization = `Bearer ${response.data.accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, clear tokens and redirect to login
        clearAuthTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
    }

    // Transform error to our standard format
    const responseData = error.response?.data as { message?: string; error?: string; statusCode?: number } | undefined;
    const errorResponse = createErrorResponse(
      responseData?.error || 'Request failed',
      responseData?.message || error.message
    );

    return Promise.reject(errorResponse);
  }
);

// Helper functions for token management
export function setAuthTokens(tokens: AuthTokens) {
  authTokens = tokens;
  if (typeof window !== 'undefined') {
    localStorage.setItem('authTokens', JSON.stringify(tokens));
  }
}

export function getAuthTokens(): AuthTokens | null {
  return authTokens;
}

export function clearAuthTokens() {
  authTokens = null;
  if (typeof window !== 'undefined') {
    localStorage.removeItem('authTokens');
  }
}

// API endpoints
export const authAPI = {
  signup: async (email: string, password: string): Promise<ApiResponse<AuthTokens>> => {
    const response = await api.post<ApiResponse<AuthApiResponse>>('/api/v1/auth/signup', {
      email,
      password,
    });
    
    // Transform the response to match our AuthTokens interface
    if (response.data.success && response.data.data) {
      return {
        ...response.data,
        data: transformAuthResponse(response.data.data),
      };
    }
    
    // Return error response without data transformation
    return {
      success: false,
      error: response.data.error || 'Signup failed',
      message: response.data.message,
    };
  },

  login: async (email: string, password: string): Promise<ApiResponse<AuthTokens>> => {
    const response = await api.post<ApiResponse<AuthApiResponse>>('/api/v1/auth/login', {
      email,
      password,
    });
    
    // Transform the response to match our AuthTokens interface
    if (response.data.success && response.data.data) {
      return {
        ...response.data,
        data: transformAuthResponse(response.data.data),
      };
    }
    
    // Return error response without data transformation
    return {
      success: false,
      error: response.data.error || 'Login failed',
      message: response.data.message,
    };
  },

  logout: async (): Promise<ApiResponse<void>> => {
    const response = await api.post<ApiResponse<void>>('/api/v1/auth/logout');
    clearAuthTokens();
    return response.data;
  },

  refreshToken: async (refreshToken: string): Promise<ApiResponse<AuthTokens>> => {
    const response = await api.post<ApiResponse<AuthApiResponse>>('/api/v1/auth/refresh', {
      refreshToken,
    });
    
    // Transform the response to match our AuthTokens interface
    if (response.data.success && response.data.data) {
      return {
        ...response.data,
        data: transformAuthResponse(response.data.data),
      };
    }
    
    return {
      success: false,
      error: response.data.error || 'Authentication failed',
      message: response.data.message,
    };
  },

  getCurrentUser: async (): Promise<ApiResponse<Profile>> => {
    const response = await api.get<ApiResponse<Profile>>('/api/v1/auth/me');
    return response.data;
  },

  resetPassword: async (email: string): Promise<ApiResponse<void>> => {
    const response = await api.post<ApiResponse<void>>('/api/v1/auth/reset-password', {
      email,
    });
    return response.data;
  },

  resetPasswordConfirm: async (token: string, newPassword: string): Promise<ApiResponse<void>> => {
    const response = await api.post<ApiResponse<void>>('/api/v1/auth/reset-password/confirm', {
      token,
      newPassword,
    });
    return response.data;
  },
};

export const tripsAPI = {
  create: async (tripData: TripCreateInput): Promise<ApiResponse<Trip>> => {
    const response = await api.post<ApiResponse<Trip>>('/api/v1/trips', tripData);
    return response.data;
  },

  list: async (): Promise<ApiResponse<Trip[]>> => {
    const response = await api.get<ApiResponse<Trip[]>>('/api/v1/trips');
    return response.data;
  },

  get: async (id: string): Promise<ApiResponse<Trip>> => {
    const response = await api.get<ApiResponse<Trip>>(`/api/v1/trips/${id}`);
    return response.data;
  },

  update: async (id: string, tripData: TripUpdateInput): Promise<ApiResponse<Trip>> => {
    const response = await api.put<ApiResponse<Trip>>(`/api/v1/trips/${id}`, tripData);
    return response.data;
  },

  delete: async (id: string): Promise<ApiResponse<void>> => {
    const response = await api.delete<ApiResponse<void>>(`/api/v1/trips/${id}`);
    return response.data;
  },

  addActivity: async (tripId: string, activityData: ActivityCreateInput): Promise<ApiResponse<Activity>> => {
    const response = await api.post<ApiResponse<Activity>>(
      `/api/v1/trips/${tripId}/activities`,
      activityData
    );
    return response.data;
  },
};

export const activitiesAPI = {
  update: async (id: string, activityData: ActivityUpdateInput): Promise<ApiResponse<Activity>> => {
    const response = await api.put<ApiResponse<Activity>>(`/api/v1/activities/${id}`, activityData);
    return response.data;
  },

  delete: async (id: string): Promise<ApiResponse<void>> => {
    const response = await api.delete<ApiResponse<void>>(`/api/v1/activities/${id}`);
    return response.data;
  },
};

export const placesAPI = {
  autocomplete: async (query: string): Promise<ApiResponse<PlaceAutocompleteResult[]>> => {
    const response = await api.get<ApiResponse<PlaceAutocompleteResult[]>>(`/api/places/autocomplete?query=${encodeURIComponent(query)}`);
    return response.data;
  },

  getDetails: async (placeId: string): Promise<ApiResponse<PlaceDetails>> => {
    const response = await api.get<ApiResponse<PlaceDetails>>(`/api/places/details/${placeId}`);
    return response.data;
  },
};

// Create unified API object
const apiClient = {
  auth: authAPI,
  trips: {
    ...tripsAPI,
    activities: {
      create: tripsAPI.addActivity,
      update: async (tripId: string, activityId: string, data: ActivityUpdateInput) => {
        const response = await api.put<ApiResponse<Activity>>(
          `/api/v1/trips/${tripId}/activities/${activityId}`,
          data
        );
        return response.data.data!;
      },
      delete: async (tripId: string, activityId: string) => {
        await api.delete(`/api/v1/trips/${tripId}/activities/${activityId}`);
      },
      addFromPlace: async (placeId: string, tripId: string) => {
        const response = await api.post<ApiResponse<Activity>>(
          `/api/v1/places/${placeId}/add-to-trip/${tripId}`
        );
        return response.data.data!;
      },
    },
    listPaginated: async (page: number = 1, limit: number = 20) => {
      const response = await api.get<ApiResponse<{
        data: Trip[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          totalPages: number;
          hasNext: boolean;
          hasPrev: boolean;
        };
      }>>(`/api/v1/trips?page=${page}&limit=${limit}`);
      return response.data.data!;
    },
  },
  activities: activitiesAPI,
  places: placesAPI,
  import: {
    parse: async (text: string, source: 'chatgpt' | 'claude' | 'gemini') => {
      const response = await api.post<ApiResponse<{ trip: Trip }>>(
        '/api/v1/import/parse',
        { text, source }
      );
      return response.data.data!;
    },
    upload: async (file: File, source: 'chatgpt' | 'claude' | 'gemini') => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('source', source);
      const response = await api.post<ApiResponse<{ trip: Trip }>>(
        '/api/v1/import/upload',
        formData
      );
      return response.data.data!;
    },
  },
};

// Also fix the wrapper functions to extract data correctly
const wrapAPI = <T>(fn: (...args: any[]) => Promise<ApiResponse<T>>) => {
  return async (...args: any[]): Promise<T> => {
    const response = await fn(...args);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'API request failed');
    }
    return response.data;
  };
};

// Create the wrapped API client with proper types
const wrappedApiClient = {
  auth: authAPI,
  trips: {
    create: wrapAPI(tripsAPI.create),
    list: async () => {
      const response = await tripsAPI.list();
      return response.data || [];
    },
    get: wrapAPI(tripsAPI.get),
    update: wrapAPI(tripsAPI.update),
    delete: async (id: string) => {
      await tripsAPI.delete(id);
    },
    addActivity: wrapAPI(tripsAPI.addActivity),
    activities: apiClient.trips.activities,
    listPaginated: apiClient.trips.listPaginated,
  },
  activities: {
    update: wrapAPI(activitiesAPI.update),
    delete: async (activityId: string) => {
      await activitiesAPI.delete(activityId);
    },
  },
  places: placesAPI,
  import: apiClient.import,
};

// Export unified API
export { wrappedApiClient as api };

// Export the base axios instance for custom requests
export default api;
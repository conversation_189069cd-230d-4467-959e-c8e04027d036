#!/usr/bin/env node

/**
 * Test script to verify the parsing hang fix
 * This tests the Enhanced AI Router Service directly
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testParsingFix() {
  console.log('🧪 Testing PDF Parsing Fix...\n');

  const API_BASE = 'http://localhost:3001/api/v1';
  
  try {
    // Step 1: Login to get auth token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const authToken = loginResponse.data.accessToken;
    console.log('✅ Login successful');

    // Step 2: Create a simple test PDF content (simulate)
    console.log('\n2. Testing with simple text content...');
    
    const testContent = `
Travel Itinerary - Paris Trip

Day 1: Arrival and Exploration
- 10:00 AM: Arrive at Charles de Gaulle Airport
- 12:00 PM: Check into Hotel Le Marais
- 2:00 PM: Lunch at Café de Flore
- 4:00 PM: Visit Louvre Museum
- 7:00 PM: Dinner at Le Comptoir du Relais

Day 2: Museums and Culture  
- 9:00 AM: Visit Notre-Dame Cathedral
- 11:00 AM: Walk along Seine River
- 1:00 PM: Lunch at L'As du Fallafel
- 3:00 PM: Visit Musée d'Orsay
- 6:00 PM: Evening at Montmartre

Budget: €1,200 for 2 people
Duration: 2 days
    `;

    // Step 3: Test the Enhanced AI Router Service directly
    console.log('\n3. Testing Enhanced AI Router Service...');
    
    // Create a mock PDF upload by sending text content
    const formData = new FormData();
    
    // Create a temporary text file to simulate PDF content
    const tempFile = path.join(__dirname, 'temp-test-content.txt');
    fs.writeFileSync(tempFile, testContent);
    
    formData.append('file', fs.createReadStream(tempFile), {
      filename: 'test-itinerary.txt',
      contentType: 'text/plain'
    });

    console.log('📤 Uploading test content...');
    
    const uploadResponse = await axios.post(`${API_BASE}/import/pdf`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${authToken}`
      },
      timeout: 30000 // 30 second timeout
    });

    const sessionId = uploadResponse.data.sessionId;
    console.log(`✅ Upload successful, session ID: ${sessionId}`);

    // Step 4: Monitor parsing progress
    console.log('\n4. Monitoring parsing progress...');
    
    let completed = false;
    let attempts = 0;
    const maxAttempts = 60; // 2 minutes max
    
    while (!completed && attempts < maxAttempts) {
      attempts++;
      
      try {
        const statusResponse = await axios.get(`${API_BASE}/import/status/${sessionId}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 10000
        });

        const status = statusResponse.data;
        console.log(`📊 Status: ${status.status}, Progress: ${status.progress}%, Step: ${status.currentStep}`);

        if (status.status === 'complete') {
          completed = true;
          console.log('\n🎉 Parsing completed successfully!');
          console.log('📋 Result summary:', {
            title: status.result?.title || 'N/A',
            activitiesCount: status.result?.activities?.length || 0,
            duration: status.result?.duration || 'N/A'
          });
          break;
        } else if (status.status === 'error') {
          console.error('\n❌ Parsing failed with error:', status.error);
          break;
        }

        // Wait 2 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        if (error.response?.status === 304) {
          // Not modified, continue waiting
          continue;
        }
        throw error;
      }
    }

    if (!completed && attempts >= maxAttempts) {
      console.error('\n⏰ Parsing timed out after 2 minutes');
      console.log('This indicates the hang issue may still exist');
    }

    // Cleanup
    fs.unlinkSync(tempFile);

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response?.data) {
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

// Run the test
testParsingFix().catch(error => {
  console.error('Test script failed:', error);
  process.exit(1);
});

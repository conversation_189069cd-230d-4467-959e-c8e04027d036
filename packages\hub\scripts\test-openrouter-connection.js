#!/usr/bin/env node

/**
 * Test script to verify OpenRouter API connection
 * This helps debug the parsing hang issue
 */

const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

async function testOpenRouterConnection() {
  console.log('🔍 Testing OpenRouter API Connection...\n');

  // Check API key
  const apiKey = process.env.OPENROUTER_API_KEY;
  console.log('API Key Status:', {
    hasKey: !!apiKey,
    keyLength: apiKey?.length || 0,
    keyPrefix: apiKey?.substring(0, 15) || 'none'
  });

  if (!apiKey) {
    console.error('❌ OPENROUTER_API_KEY not found in environment');
    process.exit(1);
  }

  // Test simple request
  try {
    console.log('\n📡 Making test request to OpenRouter...');
    
    const requestPayload = {
      model: 'moonshotai/kimi-k2:free',
      messages: [
        {
          role: 'user',
          content: 'Say hello'
        }
      ],
      temperature: 0.3,
      max_tokens: 100
    };

    console.log('Request payload:', {
      model: requestPayload.model,
      messageCount: requestPayload.messages.length,
      temperature: requestPayload.temperature,
      maxTokens: requestPayload.max_tokens
    });

    const startTime = Date.now();
    
    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestPayload,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://travelviz.app',
          'X-Title': 'TravelViz'
        },
        timeout: 30000 // 30 second timeout
      }
    );

    const duration = Date.now() - startTime;

    console.log('\n✅ OpenRouter API Response:', {
      status: response.status,
      statusText: response.statusText,
      duration: `${duration}ms`,
      hasData: !!response.data,
      hasChoices: !!response.data?.choices,
      choicesLength: response.data?.choices?.length || 0
    });

    if (response.data?.choices?.[0]?.message?.content) {
      console.log('\n📝 Response content:', response.data.choices[0].message.content);
    }

    if (response.data?.usage) {
      console.log('\n📊 Token usage:', response.data.usage);
    }

    console.log('\n🎉 OpenRouter connection test PASSED!');

  } catch (error) {
    console.error('\n❌ OpenRouter API test FAILED:');
    
    if (axios.isAxiosError(error)) {
      console.error('Axios Error:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      if (error.code === 'ECONNABORTED') {
        console.error('🕐 Request timed out - this might be the cause of the parsing hang!');
      }

      if (error.response?.status === 401) {
        console.error('🔑 Authentication failed - check your API key');
      }

      if (error.response?.status === 429) {
        console.error('⏰ Rate limit exceeded');
      }
    } else {
      console.error('Unexpected error:', error);
    }

    process.exit(1);
  }
}

// Run the test
testOpenRouterConnection().catch(error => {
  console.error('Test script failed:', error);
  process.exit(1);
});

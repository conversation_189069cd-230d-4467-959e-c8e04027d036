import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { logger } from './logger';

/**
 * Load environment variables with proper precedence:
 * 1. In development/test: .env.local > .env
 * 2. In production: .env only
 * 
 * This ensures local development uses .env.local (gitignored)
 * while production uses .env files.
 */
export function loadEnvironment(): void {
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  // Use process.cwd() which should be the package root when run via npm scripts
  const rootDir = process.cwd();
  
  // Log current working directory and resolved paths for debugging
  logger.info('Loading environment variables...', {
    cwd: process.cwd(),
    __dirname,
    rootDir,
    nodeEnv
  });
  
  // Define environment files to load in order of precedence
  const envFiles: string[] = [];
  
  if (nodeEnv === 'development' || nodeEnv === 'test') {
    // In development/test, prefer .env.local
    envFiles.push(
      path.join(rootDir, '.env.local'),
      path.join(rootDir, '.env')
    );
  } else {
    // In production, only use .env
    envFiles.push(path.join(rootDir, '.env'));
  }
  
  // Try to load each file
  let loaded = false;
  for (const envFile of envFiles) {
    logger.info(`Checking for environment file: ${envFile}`);
    if (fs.existsSync(envFile)) {
      logger.info(`Found environment file: ${envFile}`);
      const result = dotenv.config({ path: envFile });
      if (!result.error) {
        logger.info(`Environment loaded from: ${envFile}`);
        loaded = true;
        break; // Stop after first successful load
      } else {
        logger.warn(`Failed to load ${envFile}: ${result.error.message}`);
      }
    } else {
      logger.info(`Environment file not found: ${envFile}`);
    }
  }
  
  if (!loaded) {
    logger.warn('No environment file found, using process environment only');
  }
  
  // Log environment status (without exposing sensitive values)
  const envStatus = {
    NODE_ENV: nodeEnv,
    OPENROUTER_API_KEY: !!process.env.OPENROUTER_API_KEY,
    GOOGLE_GEMINI_API_KEY: !!process.env.GOOGLE_GEMINI_API_KEY,
    SUPABASE_URL: !!process.env.SUPABASE_URL,
    UPSTASH_REDIS_URL: !!process.env.UPSTASH_REDIS_URL,
  };
  
  logger.info('Environment status:', envStatus);
  
  // Log detailed key information for debugging
  const keyDetails = {
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY ? `Set (length: ${process.env.OPENROUTER_API_KEY.length})` : 'Missing',
    GOOGLE_GEMINI_API_KEY: process.env.GOOGLE_GEMINI_API_KEY ? `Set (length: ${process.env.GOOGLE_GEMINI_API_KEY.length})` : 'Missing',
    SUPABASE_URL: process.env.SUPABASE_URL ? `Set (length: ${process.env.SUPABASE_URL.length})` : 'Missing',
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? `Set (length: ${process.env.SUPABASE_SERVICE_ROLE_KEY.length})` : 'Missing',
    UPSTASH_REDIS_URL: process.env.UPSTASH_REDIS_URL ? `Set (length: ${process.env.UPSTASH_REDIS_URL.length})` : 'Missing',
    JWT_SECRET: process.env.JWT_SECRET ? `Set (length: ${process.env.JWT_SECRET.length})` : 'Missing',
    MAPBOX_ACCESS_TOKEN: process.env.MAPBOX_ACCESS_TOKEN ? `Set (length: ${process.env.MAPBOX_ACCESS_TOKEN.length})` : 'Missing',
    GOOGLE_PLACES_API_KEY: process.env.GOOGLE_PLACES_API_KEY ? `Set (length: ${process.env.GOOGLE_PLACES_API_KEY.length})` : 'Missing',
  };
  
  logger.info('Environment key details:', keyDetails);
}
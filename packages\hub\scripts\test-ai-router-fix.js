#!/usr/bin/env node

/**
 * Direct test of Enhanced AI Router Service to verify the hang fix
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

async function testAIRouterFix() {
  console.log('🧪 Testing Enhanced AI Router Service Fix...\n');

  try {
    // Import the service
    const { enhancedAIRouterService } = await import('../dist/services/enhanced-ai-router.service.js');
    
    console.log('✅ Enhanced AI Router Service imported successfully');

    // Test content
    const testContent = `
Travel Itinerary - Tokyo Trip

Day 1: Arrival
- 10:00 AM: Arrive at Narita Airport
- 12:00 PM: Check into hotel in Shibuya
- 2:00 PM: Lunch at local ramen shop
- 4:00 PM: Visit Senso-ji Temple
- 7:00 PM: Dinner in Harajuku

Day 2: Culture
- 9:00 AM: Visit Tokyo National Museum
- 11:00 AM: Walk in Ueno Park
- 1:00 PM: Lunch at sushi restaurant
- 3:00 PM: Visit Meiji Shrine
- 6:00 PM: Evening in Shinjuku

Budget: ¥150,000 for 2 people
Duration: 2 days
    `;

    console.log('📝 Test content prepared');
    console.log(`📊 Content length: ${testContent.length} characters`);

    // Test the parsing with timeout
    console.log('\n🚀 Starting AI parsing test...');
    
    const startTime = Date.now();
    
    // Add timeout wrapper
    const parsePromise = enhancedAIRouterService.parseContent(testContent, 'test', 'test-user-id');
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Test timeout after 60 seconds'));
      }, 60000);
    });

    const result = await Promise.race([parsePromise, timeoutPromise]);
    
    const duration = Date.now() - startTime;
    
    console.log(`\n🎉 Parsing completed successfully in ${duration}ms!`);
    console.log('📋 Result summary:', {
      title: result.title || 'N/A',
      activitiesCount: result.activities?.length || 0,
      duration: result.duration || 'N/A',
      hasMetadata: !!result.metadata
    });

    if (result.activities && result.activities.length > 0) {
      console.log('\n📅 Sample activities:');
      result.activities.slice(0, 3).forEach((activity, index) => {
        console.log(`  ${index + 1}. ${activity.title} (${activity.type})`);
      });
    }

    console.log('\n✅ Enhanced AI Router Service fix verification PASSED!');
    console.log('🔧 The hang issue has been resolved');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.message.includes('timeout')) {
      console.error('🕐 The parsing is still hanging - fix may not be complete');
    } else if (error.message.includes('OpenRouter')) {
      console.error('🔑 OpenRouter API issue - check API key and model availability');
    } else if (error.message.includes('json_object')) {
      console.error('📝 JSON format issue - model may not support structured output');
    }
    
    console.error('\nStack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testAIRouterFix().catch(error => {
  console.error('Test script failed:', error);
  process.exit(1);
});

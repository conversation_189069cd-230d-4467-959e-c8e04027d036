import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { redis } from '../config/redis';
import { 
  createMockParsedTrip,
  createMockParseSession,
  advanceTimeAndFlushPromises,
  waitForAsync
} from '../../test/utils/test-helpers';

// Mock dependencies
vi.mock('../config/redis', () => ({
  redis: {
    publish: vi.fn().mockResolvedValue(1),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    expire: vi.fn().mockResolvedValue(1),
  },
}));
vi.mock('../utils/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
  },
}));
vi.mock('../lib/supabase', () => ({
  getSupabaseClient: vi.fn(() => ({
    from: vi.fn(() => ({
      insert: vi.fn(() => ({ error: null })),
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => ({ data: null, error: new Error('Not found') }))
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({ error: null }))
      }))
    }))
  }))
}));

// Since we can't directly test the private publishProgress method,
// we'll test it through the public methods that use it
import { logger } from '../utils/logger';
import { AI_CONFIG } from '../config/ai.config';

// Mock AI_CONFIG to avoid import errors
vi.mock('../config/ai.config', () => ({
  AI_CONFIG: {
    progressMessages: {
      initializing: 'Starting import process...',
      extracting: 'Extracting trip information...',
      parsing: 'Analyzing your conversation...',
      enhancing: 'Enhancing trip details...',
      validating: 'Validating trip data...',
      finalizing: 'Finalizing your itinerary...'
    }
  }
}));

describe('AI Parser SSE Progress Updates', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Progress Publishing', () => {
    it('should publish progress updates through Redis pub/sub', async () => {
      const sessionId = 'test-session-123';
      const progressData = {
        sessionId,
        status: 'parsing',
        progress: 40,
        message: 'Analyzing your conversation...',
        currentStep: 'parsing'
      };

      // Simulate progress publish (since we can't access private method directly)
      await redis.publish(`parse:progress:${sessionId}`, JSON.stringify(progressData));

      expect(redis.publish).toHaveBeenCalledWith(
        `parse:progress:${sessionId}`,
        JSON.stringify(progressData)
      );
    });

    it('should publish progress at each parsing stage', async () => {
      const sessionId = 'test-session-123';
      
      // Simulate the sequence of progress updates
      const progressStages = [
        { status: 'initializing', progress: 0, message: AI_CONFIG.progressMessages.initializing },
        { status: 'extracting', progress: 20, message: AI_CONFIG.progressMessages.extracting },
        { status: 'parsing', progress: 40, message: AI_CONFIG.progressMessages.parsing },
        { status: 'enhancing', progress: 60, message: AI_CONFIG.progressMessages.enhancing },
        { status: 'validating', progress: 80, message: AI_CONFIG.progressMessages.validating },
        { status: 'finalizing', progress: 90, message: AI_CONFIG.progressMessages.finalizing },
        { status: 'complete', progress: 100, message: 'Import completed successfully!' }
      ];

      for (const stage of progressStages) {
        await redis.publish(
          `parse:progress:${sessionId}`,
          JSON.stringify({
            sessionId,
            ...stage,
            currentStep: stage.status
          })
        );
      }

      expect(redis.publish).toHaveBeenCalledTimes(progressStages.length);
      
      // Verify each stage was published
      progressStages.forEach((stage, index) => {
        expect(redis.publish).toHaveBeenNthCalledWith(
          index + 1,
          `parse:progress:${sessionId}`,
          expect.stringContaining(stage.message)
        );
      });
    });

    it('should handle Redis publish failures gracefully', async () => {
      const sessionId = 'test-session-123';
      vi.mocked(redis.publish).mockRejectedValueOnce(new Error('Redis connection failed'));

      // Simulate progress publish with error handling
      try {
        await redis.publish(`parse:progress:${sessionId}`, JSON.stringify({ progress: 50 }));
      } catch (error) {
        // Log error instead of throwing
        logger.error('Failed to publish progress', { error, sessionId });
      }

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to publish progress',
        expect.objectContaining({
          error: expect.any(Error),
          sessionId
        })
      );
    });

    it('should include timestamp in progress updates', async () => {
      const sessionId = 'test-session-123';
      const beforeTime = Date.now();

      await redis.publish(
        `parse:progress:${sessionId}`,
        JSON.stringify({
          sessionId,
          status: 'parsing',
          progress: 50,
          message: 'Processing...',
          timestamp: new Date().toISOString()
        })
      );

      const publishCall = vi.mocked(redis.publish).mock.calls[0][1];
      const publishData = JSON.parse(publishCall);
      
      expect(publishData).toHaveProperty('timestamp');
      expect(new Date(publishData.timestamp).getTime()).toBeGreaterThanOrEqual(beforeTime);
    });
  });

  describe('Progress Channel Management', () => {
    it('should use correct channel naming convention', async () => {
      const sessionId = 'session-abc-123';
      const expectedChannel = `parse:progress:${sessionId}`;

      await redis.publish(expectedChannel, JSON.stringify({ progress: 25 }));

      expect(redis.publish).toHaveBeenCalledWith(
        expectedChannel,
        expect.any(String)
      );
    });

    it('should handle concurrent progress updates', async () => {
      const sessions = ['session-1', 'session-2', 'session-3'];
      
      // Simulate concurrent progress updates
      await Promise.all(
        sessions.map(sessionId =>
          redis.publish(
            `parse:progress:${sessionId}`,
            JSON.stringify({ sessionId, progress: 50 })
          )
        )
      );

      expect(redis.publish).toHaveBeenCalledTimes(3);
      
      // Verify each session has its own channel
      sessions.forEach(sessionId => {
        expect(redis.publish).toHaveBeenCalledWith(
          `parse:progress:${sessionId}`,
          expect.stringContaining(sessionId)
        );
      });
    });
  });

  describe('Error Progress Updates', () => {
    it('should publish error status on parsing failure', async () => {
      const sessionId = 'test-session-123';
      const errorMessage = 'Failed to parse conversation: Invalid format';

      await redis.publish(
        `parse:progress:${sessionId}`,
        JSON.stringify({
          sessionId,
          status: 'error',
          progress: 0,
          message: errorMessage,
          error: errorMessage,
          currentStep: 'parsing'
        })
      );

      const publishCall = vi.mocked(redis.publish).mock.calls[0];
      const publishData = JSON.parse(publishCall[1]);

      expect(publishData).toMatchObject({
        status: 'error',
        progress: 0,
        error: errorMessage
      });
    });

    it('should distinguish between different error types', async () => {
      const sessionId = 'test-session-123';
      
      const errorTypes = [
        { type: 'validation', message: 'Input text is too short' },
        { type: 'timeout', message: 'Parsing timed out after 30 seconds' },
        { type: 'rate_limit', message: 'Rate limit exceeded' },
        { type: 'api_error', message: 'AI service temporarily unavailable' }
      ];

      for (const error of errorTypes) {
        await redis.publish(
          `parse:progress:${sessionId}`,
          JSON.stringify({
            sessionId,
            status: 'error',
            progress: 0,
            message: error.message,
            errorType: error.type
          })
        );
      }

      expect(redis.publish).toHaveBeenCalledTimes(errorTypes.length);
    });
  });

  describe('Progress Update Sequencing', () => {
    it('should maintain correct progress sequence', async () => {
      const sessionId = 'test-session-123';
      const progressValues = [0, 20, 40, 60, 80, 90, 100];
      
      for (const progress of progressValues) {
        await redis.publish(
          `parse:progress:${sessionId}`,
          JSON.stringify({ sessionId, progress })
        );
      }

      const calls = vi.mocked(redis.publish).mock.calls;
      const actualProgress = calls.map(call => JSON.parse(call[1]).progress);
      
      expect(actualProgress).toEqual(progressValues);
    });

    it('should not publish duplicate progress values', async () => {
      const sessionId = 'test-session-123';
      let lastProgress = -1;
      
      const progressUpdates = [0, 20, 20, 40, 40, 40, 60, 80, 100];
      
      for (const progress of progressUpdates) {
        if (progress !== lastProgress) {
          await redis.publish(
            `parse:progress:${sessionId}`,
            JSON.stringify({ sessionId, progress })
          );
          lastProgress = progress;
        }
      }

      // Should only publish unique progress values
      expect(redis.publish).toHaveBeenCalledTimes(6); // [0, 20, 40, 60, 80, 100]
    });
  });

  describe('Progress Metadata', () => {
    it('should include parsing statistics in progress updates', async () => {
      const sessionId = 'test-session-123';
      
      await redis.publish(
        `parse:progress:${sessionId}`,
        JSON.stringify({
          sessionId,
          status: 'parsing',
          progress: 50,
          message: 'Analyzing activities...',
          stats: {
            activitiesFound: 12,
            daysPlanned: 5,
            locationsIdentified: 8
          }
        })
      );

      const publishData = JSON.parse(vi.mocked(redis.publish).mock.calls[0][1]);
      expect(publishData.stats).toEqual({
        activitiesFound: 12,
        daysPlanned: 5,
        locationsIdentified: 8
      });
    });

    it('should include model information in progress', async () => {
      const sessionId = 'test-session-123';
      
      await redis.publish(
        `parse:progress:${sessionId}`,
        JSON.stringify({
          sessionId,
          status: 'parsing',
          progress: 30,
          message: 'Using Gemini Flash 2.0 for parsing...',
          model: {
            id: 'gemini-2.0-flash-exp',
            provider: 'google',
            isFree: true
          }
        })
      );

      const publishData = JSON.parse(vi.mocked(redis.publish).mock.calls[0][1]);
      expect(publishData.model).toMatchObject({
        id: 'gemini-2.0-flash-exp',
        isFree: true
      });
    });
  });

  describe('Connection Cleanup', () => {
    it('should handle client disconnect during progress updates', async () => {
      const sessionId = 'test-session-123';
      
      // Simulate progress updates
      await redis.publish(
        `parse:progress:${sessionId}`,
        JSON.stringify({ progress: 25 })
      );

      // Simulate client disconnect
      const disconnectMarker = `parse:disconnect:${sessionId}`;
      await redis.set(disconnectMarker, '1', { ex: 300 });

      // Future progress updates should check for disconnect
      const isDisconnected = await redis.get(disconnectMarker);
      
      if (isDisconnected) {
        logger.info('Client disconnected, stopping progress updates', { sessionId });
      }

      // Verify the disconnect marker was set
      expect(redis.set).toHaveBeenCalledWith(disconnectMarker, '1', { ex: 300 });
    });

    it('should clean up progress channels after completion', async () => {
      const sessionId = 'test-session-123';
      const progressKey = `parse:progress:last:${sessionId}`;
      
      // Simulate final progress
      await redis.publish(
        `parse:progress:${sessionId}`,
        JSON.stringify({ progress: 100, status: 'complete' })
      );

      // Store last progress for potential reconnection
      await redis.set(
        progressKey,
        JSON.stringify({ progress: 100, status: 'complete' }),
        { ex: 300 } // 5 minutes TTL
      );

      expect(redis.set).toHaveBeenCalledWith(
        progressKey,
        expect.any(String),
        { ex: 300 }
      );
    });
  });
});
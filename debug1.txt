PS C:\Users\<USER>\Travelviz\Travelviz> pnpm dev

> travelviz@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz
> node scripts/dev-services-enhanced.js

Starting TravelViz development servers...
[hub] Starting API server...
[web] Starting Next.js app...

⠴ Waiting for services to start...[web] > @travelviz/web@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz\packages\web
[web] > next dev
[hub] > @travelviz/hub@1.0.0 dev C:\Users\<USER>\Travelviz\Travelviz\packages\hub
[hub] 🔄 Compiling TypeScript...
[hub] > npx tsx watch src/index.ts
⠧ Waiting for services to start...[web]    ▲ Next.js 15.3.5
[web]    - Local:        http://localhost:3000
[web]    - Network:      http://*************:3000
[web]    - Environments: .env.local
[web] 🔄 Initializing Next.js...
⠇ Waiting for services to start...[hub] 2025-07-17T20:41:40.176Z [WARN] Travelpayouts API key not configured 
⠏ Waiting for services to start...[web]  ✓ Ready in 2.6s
⠋ Waiting for services to start...[web]  ✓ Compiled /middleware in 188ms
[hub] 2025-07-17T20:41:40.482Z [WARN] Mapbox access token not found - geocoding will be disabled 
⠙ Waiting for services to start...[web]  ✓ Compiled (108 modules)
[hub] 2025-07-17T20:41:40.591Z [INFO] Usage tracking service initialized with reset notification listener 
⠹ Waiting for services to start...[hub] 2025-07-17T20:41:40.594Z [INFO] Model-specific prompts initialized {
[hub]   "promptCount": 5,
[hub]   "models": [
[hub]     "moonshotai/kimi-k2:free",
[hub]     "google/gemini-2.5-pro",
[hub]     "google/gemini-2.5-flash",
[hub]     "google/gemini-2.0-flash",
[hub]     "openai/gpt-4.1-nano"
[hub]   ]
[hub] }
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
⠴ Waiting for services to start...[hub] 2025-07-17T20:41:40.959Z [INFO] Loading environment variables... {
[hub]   "cwd": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub",
[hub]   "__dirname": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub\\src\\utils",
[hub]   "rootDir": "C:\\Users\\<USER>\\Travelviz\\Travelviz\\packages\\hub",
[hub]   "nodeEnv": "development"
[hub] }
[hub] 2025-07-17T20:41:40.960Z [INFO] Checking for environment file: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T20:41:40.960Z [INFO] Found environment file: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T20:41:40.961Z [INFO] Environment loaded from: C:\Users\<USER>\Travelviz\Travelviz\packages\hub\.env.local
[hub] 2025-07-17T20:41:40.961Z [INFO] Environment status: {
[hub]   "NODE_ENV": "development",
[hub]   "OPENROUTER_API_KEY": true,
[hub]   "GOOGLE_GEMINI_API_KEY": true,
[hub]   "SUPABASE_URL": true,
[hub]   "UPSTASH_REDIS_URL": true
[hub] }
[hub] 2025-07-17T20:41:40.961Z [INFO] Environment key details: {
[hub]   "OPENROUTER_API_KEY": "Set (length: 73)",
[hub]   "GOOGLE_GEMINI_API_KEY": "Set (length: 39)",
[hub]   "SUPABASE_URL": "Set (length: 40)",
[hub]   "SUPABASE_SERVICE_ROLE_KEY": "Set (length: 219)",
[hub]   "UPSTASH_REDIS_URL": "Set (length: 34)",
[hub]   "JWT_SECRET": "Set (length: 60)",
[hub]   "MAPBOX_ACCESS_TOKEN": "Set (length: 92)",
[hub]   "GOOGLE_PLACES_API_KEY": "Set (length: 39)"
[hub] }
[hub] 2025-07-17T20:41:40.961Z [INFO] Environment variable JWT_SECRET is set (value hidden)
[hub] 2025-07-17T20:41:40.961Z [INFO] Environment variable SUPABASE_URL is set
[hub] 2025-07-17T20:41:40.961Z [INFO] Environment variable SUPABASE_SERVICE_ROLE_KEY is set (value hidden)
[hub] 2025-07-17T20:41:40.961Z [INFO] Environment variable SUPABASE_JWT_SECRET is set (value hidden)
[hub] 2025-07-17T20:41:40.961Z [INFO] Environment variable OPENROUTER_API_KEY is set (value hidden)
[hub] 2025-07-17T20:41:40.962Z [INFO] Environment variable MAPBOX_ACCESS_TOKEN is set (value hidden)
[hub] 2025-07-17T20:41:40.962Z [INFO] Environment variable GOOGLE_PLACES_API_KEY is set (value hidden)
[hub] 2025-07-17T20:41:40.962Z [INFO] Environment variable PORT is set
[hub] 2025-07-17T20:41:40.962Z [INFO] Environment variable NODE_ENV is set
[hub] 2025-07-17T20:41:40.962Z [INFO] JWT_SECRET validation passed
[hub] 2025-07-17T20:41:40.962Z [INFO] MAPBOX_ACCESS_TOKEN validation passed
[hub] 2025-07-17T20:41:40.962Z [INFO] GOOGLE_PLACES_API_KEY validation passed
[hub] 2025-07-17T20:41:40.962Z [INFO] All required environment variables are properly configured
[hub] 2025-07-17T20:41:40.970Z [INFO] 🚀 TravelViz Hub API server running on port 3001
[hub] 2025-07-17T20:41:40.971Z [INFO] 📍 Health check: http://localhost:3001/health
[hub] 2025-07-17T20:41:40.971Z [INFO] 🔧 Environment: development
[hub] 2025-07-17T20:41:40.971Z [INFO] 🌐 CORS enabled for: http://localhost:3000
⠦ Waiting for services to start...✅ [hub] Ready in 5.1s - http://localhost:3001
[hub] 2025-07-17T20:41:41.034Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/health",
[hub]   "statusCode": 200,
[hub]   "responseTime": "1.849ms",
[hub]   "ip": "::1"
[hub] }
[hub] GET /health 200 1.849ms eeb81382-975b-43be-a3a5-cba32d603e6a
[web] 🔍 Middleware: No auth cookie found for /
⠧ Waiting for services to start...[web] 🔨 Compiling pages...
[web]  ○ Compiling / ...
⠹ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
⠏ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware: No auth cookie found for /
⠙ Waiting for services to start...[web] ✨ Compilation complete
[web]  ✓ Compiled / in 3.2s (1452 modules)
⠦ Waiting for services to start...[web] Performance monitoring initialized
[web] Performance monitoring initialized
⠧ Waiting for services to start...[web] Performance monitoring initialized
[web] Performance monitoring initialized
⠇ Waiting for services to start...[web] Performance monitoring initialized
⠏ Waiting for services to start...[web] Performance monitoring initialized
[web]  GET / 200 in 1021ms
[web]  GET / 200 in 1095ms
⠹ Waiting for services to start...[web] 🔍 Middleware: No auth cookie found for /
[web] 🔍 Middleware auth check: {
[web]   pathname: '/login',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🏠 Redirecting to dashboard: { pathname: '/login', isAuthenticated: true, isAuthRoute: true }
[web] 🔍 Middleware: No auth cookie found for /
⠸ Waiting for services to start...[web]  GET / 200 in 53ms
[web] 🔍 Middleware auth check: {
[web]   pathname: '/dashboard',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web]  ✓ Compiled in 1280ms (683 modules)
⠼ Waiting for services to start...[web] Performance monitoring initialized
⠴ Waiting for services to start...[web] Performance monitoring initialized
✅ [web] Ready in 9.4s - http://localhost:3000

🚀 All services ready in 9.4s!
[hub] API: http://localhost:3001
[web] App: http://localhost:3000

Press Ctrl+C to stop all services

[web]  GET / 200 in 281ms
[web] 🔍 Middleware: No auth cookie found for /
[web] 🔨 Compiling pages...
[web]  ○ Compiling /dashboard ...
[web] 🔍 Middleware auth check: {
[web]   pathname: '/',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] ✨ Compilation complete
[web]  ✓ Compiled /dashboard in 2.7s (3623 modules)
[web] Performance monitoring initialized
[web]  GET / 200 in 718ms
[web]  GET /dashboard 200 in 3378ms
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/login',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🏠 Redirecting to dashboard: { pathname: '/login', isAuthenticated: true, isAuthRoute: true }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/dashboard',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web]  GET /dashboard 200 in 15ms
[hub] 2025-07-17T20:41:50.321Z [WARN] Authentication failed - no token provided {
[hub]   "authHeader": "missing",
[hub]   "path": "/"
[hub] }
[hub] GET /api/v1/trips?page=1&limit=20 401 1.537ms 3096c87e-c2be-42ad-b758-46fa42faad5b
[hub] 2025-07-17T20:41:50.322Z [WARN] HTTP Request Error {
[hub]   "method": "GET",
[hub]   "url": "/?page=1&limit=20",
[hub]   "statusCode": 401,
[hub]   "responseTime": "1.537ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔍 Middleware auth check: {
[web]   pathname: '/import',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[web] 🔨 Compiling pages...
[web]  ○ Compiling /import ...
[web] ✨ Compilation complete
[web]  ✓ Compiled /import in 1605ms (4903 modules)
[web]  GET /import 200 in 1751ms
[web] 🔍 Middleware: No auth cookie found for /site.webmanifest
[web] 🔍 Middleware auth check: {
[web]   pathname: '/icon-192.png',
[web]   hasAuthCookie: true,
[web]   isAuthenticated: true,
[web]   hasAccessToken: true,
[web]   cookieValue: '{"state":{"user":{"id":"697b40b3-42d7-4b32-ad49-0220c2313643","email":"<EMAIL>"},"accessToke...',
[web]   authDataKeys: [ 'state', 'version' ],
[web]   authStateKeys: [ 'user', 'accessToken', 'refreshToken', 'isAuthenticated' ],
[web]   authStateIsAuthenticated: true,
[web]   authStateAccessToken: 'present'
[web] }
[hub] 2025-07-17T20:42:00.448Z [INFO] Supabase connection pool initialized {
[hub]   "maxConnections": 50,
[hub]   "minConnections": 5
[hub] }
[hub] 2025-07-17T20:42:00.728Z [INFO] Creating AIParserService {
[hub]   "hasGeminiKey": true,
[hub]   "hasOpenRouterKey": true,
[hub]   "nodeEnv": "development"
[hub] }
[hub] 2025-07-17T20:42:00.729Z [INFO] AIParserService initialized {
[hub]   "circuitBreakerState": "CLOSED",
[hub]   "enhancedAIRouterAvailable": true
[hub] }
[hub] Warning: Indexing all PDF objects
[hub] 2025-07-17T20:42:00.959Z [INFO] PDF text extracted successfully {
[hub]   "pages": 22,
[hub]   "textLength": 27230
[hub] }
[hub] 2025-07-17T20:42:01.221Z [INFO] Redis connection pool initialized {
[hub]   "minConnections": 5,
[hub]   "totalConnections": 6
[hub] }
[hub] 2025-07-17T20:42:01.385Z [INFO] Parse session created successfully {
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "userId": "697b40b3-42d7-4b32-ad49-0220c2313643",
[hub]   "source": "gemini",
[hub]   "contentLength": 27230
[hub] }
[hub] 2025-07-17T20:42:01.439Z [INFO] [Cache] Redis connection pool cache initialized 
[hub] 2025-07-17T20:42:01.539Z [INFO] Starting background parse {
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "source": "gemini"
[hub] }
[hub] 2025-07-17T20:42:01.539Z [INFO] parseAsync started {
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "contentLength": 27230,
[hub]   "source": "gemini"
[hub] }
[hub] 2025-07-17T20:42:01.565Z [INFO] PDF import session created {
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "userId": "697b40b3-42d7-4b32-ad49-0220c2313643",
[hub]   "source": "gemini",
[hub]   "pageCount": 22
[hub] }
[hub] 2025-07-17T20:42:01.566Z [WARN] Slow API request {
[hub]   "method": "POST",
[hub]   "path": "/pdf",
[hub]   "duration": "1121.09ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:01.566Z [INFO] HTTP Request {
[hub]   "method": "POST",
[hub]   "url": "/pdf",
[hub]   "statusCode": 200,
[hub]   "responseTime": "1121.418ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] POST /api/v1/import/pdf 200 1121.418ms 9aa94e2c-604c-4b62-8794-1840203338f8
[hub] 2025-07-17T20:42:01.649Z [INFO] Session status updated successfully {
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "status": "processing",
[hub]   "progress": 10,
[hub]   "currentStep": "initializing"
[hub] }
[hub] 2025-07-17T20:42:01.680Z [INFO] Starting AI parse with Enhanced AI Router Service {
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "contentLength": 27230,
[hub]   "source": "gemini",
[hub]   "hasGeminiKey": true,
[hub]   "hasOpenRouterKey": true
[hub] }
[hub] 2025-07-17T20:42:01.766Z [INFO] Session status updated successfully {
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "status": "processing",
[hub]   "progress": 20,
[hub]   "currentStep": "extracting"
[hub] }
[hub] 2025-07-17T20:42:01.847Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "269.35ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:01.848Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 200,
[hub]   "responseTime": "269.642ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 200 269.642ms 8fb1574c-c389-4f0f-80b4-391a59662666
[hub] 2025-07-17T20:42:01.879Z [INFO] Session status updated successfully {
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "status": "processing",
[hub]   "progress": 40,
[hub]   "currentStep": "parsing"
[hub] }
[hub] 2025-07-17T20:42:01.907Z [INFO] Using Enhanced AI Router for intelligent model selection {
[hub]   "source": "gemini",
[hub]   "contentLength": 27230,
[hub]   "sessionId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd"
[hub] }
[hub] 2025-07-17T20:42:01.907Z [INFO] Enhanced AI Router Service: Starting parseContent {
[hub]   "contentLength": 27230,
[hub]   "source": "gemini",
[hub]   "userId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "timestamp": "2025-07-17T20:42:01.907Z"
[hub] }
[hub] 2025-07-17T20:42:01.907Z [INFO] Enhanced AI Router Service: Selecting optimal model...
[hub] 2025-07-17T20:42:01.908Z [INFO] Starting model selection {
[hub]   "contentLength": 27230,
[hub]   "complexity": "very_complex",
[hub]   "estimatedTokens": 14808
[hub] }
[hub] 2025-07-17T20:42:02.036Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "186.752ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 186.752ms 42e9d6d8-f75f-4f3c-9fd2-603ef71f27f0
[hub] 2025-07-17T20:42:02.235Z [INFO] Model selected for parsing {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "provider": "moonshot",
[hub]   "reason": "Primary free tier model - under daily limit",
[hub]   "estimatedCost": 0,
[hub]   "userId": "abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd"
[hub] }
[hub] 2025-07-17T20:42:02.235Z [INFO] Enhanced AI Router Service: About to execute with selected model {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "timestamp": "2025-07-17T20:42:02.235Z"
[hub] }
[hub] 2025-07-17T20:42:02.235Z [INFO] Enhanced AI Router Service: Calling executeWithModel... {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T20:42:02.235Z [INFO] Enhanced AI Router Service: executeWithModel started {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "contentLength": 27230,
[hub]   "timestamp": "2025-07-17T20:42:02.235Z"
[hub] }
[hub] 2025-07-17T20:42:02.235Z [INFO] Enhanced AI Router Service: Getting model configuration... {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T20:42:02.235Z [INFO] Enhanced AI Router Service: Prompts retrieved {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "systemPromptLength": 1188,
[hub]   "formatInstructionsLength": 117
[hub] }
[hub] 2025-07-17T20:42:02.235Z [INFO] Enhanced AI Router Service: Full prompt built {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "fullPromptLength": 28557,
[hub]   "tokenEstimate": {
[hub]     "inputTokens": 6808,
[hub]     "outputTokens": 8000,
[hub]     "complexity": "very_complex"
[hub]   }
[hub] }
[hub] 2025-07-17T20:42:02.235Z [INFO] Enhanced AI Router Service: Determining provider for model {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T20:42:02.235Z [INFO] Enhanced AI Router Service: Calling OpenRouter API for Moonshot... {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T20:42:02.236Z [INFO] Enhanced AI Router Service: callOpenRouterAPI started {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "contentLength": 27230,
[hub]   "systemPromptLength": 1188,
[hub]   "timestamp": "2025-07-17T20:42:02.236Z"
[hub] }
[hub] 2025-07-17T20:42:02.236Z [INFO] Enhanced AI Router Service: Checking API key... {
[hub]   "hasApiKey": true,
[hub]   "keyLength": 73,
[hub]   "keyPrefix": "sk-or-v1-1"
[hub] }
[hub] 2025-07-17T20:42:02.236Z [INFO] Making OpenRouter API request {
[hub]   "modelId": "moonshotai/kimi-k2:free",
[hub]   "contentLength": 27230,
[hub]   "systemPromptLength": 1188,
[hub]   "timeout": 60000
[hub] }
[hub] 2025-07-17T20:42:02.236Z [INFO] Enhanced AI Router Service: Building request payload... {
[hub]   "modelId": "moonshotai/kimi-k2:free"
[hub] }
[hub] 2025-07-17T20:42:02.236Z [INFO] Enhanced AI Router Service: Request payload built {
[hub]   "model": "moonshotai/kimi-k2:free",
[hub]   "messageCount": 2,
[hub]   "temperature": 0.3,
[hub]   "maxTokens": 4000,
[hub]   "systemMessageLength": 1188,
[hub]   "userMessageLength": 27230
[hub] }
[hub] 2025-07-17T20:42:02.236Z [INFO] Enhanced AI Router Service: About to make axios POST request... {
[hub]   "url": "https://openrouter.ai/api/v1/chat/completions",
[hub]   "timeout": 60000,
[hub]   "timestamp": "2025-07-17T20:42:02.236Z"
[hub] }
[hub] 2025-07-17T20:42:02.246Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "209.11ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:02.247Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "209.418ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 209.418ms 781f737e-6e5c-4502-b650-04b9305e210b
[hub] 2025-07-17T20:42:02.469Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "219.98ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:02.469Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "220.214ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 220.214ms 3cf28d52-d2d5-42e5-966e-07b43944793a
[hub] 2025-07-17T20:42:02.643Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "172.052ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 172.052ms f625dbaa-fb9f-47ce-80f4-fd0fd7bebbfe
[hub] 2025-07-17T20:42:02.833Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "188.911ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 188.911ms e814a4cc-ab8a-4b7d-96ad-9c6ec5d106e0
[hub] 2025-07-17T20:42:02.989Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "155.236ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 155.236ms f0b4e869-1061-4fa0-946e-15d7a66bb933
[hub] 2025-07-17T20:42:03.169Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "179.85ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 179.850ms 2c4ab0c0-5244-42e1-a5ed-7ff4c9110828
[hub] 2025-07-17T20:42:03.353Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "182.445ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 182.445ms bc0de1c6-2adf-475d-a7ee-0d874ad4aa00
[hub] 2025-07-17T20:42:03.571Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "217.04ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:03.571Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "217.313ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 217.313ms 10c8a76c-a680-4e78-9dad-880a4f89e7ba
[hub] 2025-07-17T20:42:03.772Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "199.785ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 199.785ms 86453f0a-09bc-40bc-9494-a6e7eebd01fa
[hub] 2025-07-17T20:42:04.016Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "243.44ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:04.017Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "243.668ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 243.668ms 5691b9ba-defa-481b-915d-8704d0ac9de7
[hub] 2025-07-17T20:42:04.225Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "207.33ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:04.225Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "207.533ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 207.533ms 3eabffe9-315d-496e-ba54-7653c39e911b
[hub] 2025-07-17T20:42:04.420Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "194.246ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 194.246ms 867cf0a8-1dc9-4a56-8aa4-8a80414dc4a9
[hub] 2025-07-17T20:42:04.674Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "253.32ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:04.674Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "253.511ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 253.511ms c88b1a6f-e936-46d1-aa5f-5bb7fc81c012
[hub] 2025-07-17T20:42:04.931Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "256.51ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:04.932Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "256.713ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 256.713ms 57040eba-f31f-4df9-9dae-1157c2b8a79c
[hub] 2025-07-17T20:42:05.127Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "194.036ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 194.036ms 1b8b6750-e88b-4dc6-8500-b1ff9ea66945
[hub] 2025-07-17T20:42:05.306Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "178.131ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 178.131ms e969cc43-e2d4-4d65-bb87-875a8424a5e0
[hub] 2025-07-17T20:42:05.495Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "187.995ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 187.995ms b5b8fe46-eb63-4610-a14f-83d51e69e684
[hub] 2025-07-17T20:42:05.652Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "156.483ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 156.483ms 1257a08c-786b-4a9b-884d-608f0818a30d
[hub] 2025-07-17T20:42:05.822Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "168.525ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 168.525ms ab9f59bb-6d37-4a9c-aac9-2f2731a81617
[hub] 2025-07-17T20:42:06.078Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "255.18ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:06.079Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "255.364ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 255.364ms 0ddeba05-0683-44ea-a886-4b2064a9fed7
[hub] 2025-07-17T20:42:06.280Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "201.27ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:06.281Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "201.466ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 201.466ms 75582e72-a8bd-4d52-8457-395e0c08c7cc
[hub] 2025-07-17T20:42:06.464Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "181.907ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 181.907ms ffcfc5e0-d0cd-42f8-86f9-880863dc79c8
[hub] 2025-07-17T20:42:06.636Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "170.968ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 170.968ms 4637b844-3dc7-45bc-997b-dbe909ddfe89
[hub] 2025-07-17T20:42:06.841Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "204.37ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:06.841Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "204.567ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 204.567ms 288ed1ee-9b13-49ae-b9e1-602000980c80
[hub] 2025-07-17T20:42:07.011Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "168.84ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 168.840ms ce644fc4-52df-4b01-bfb5-4951a0c43a84
[hub] 2025-07-17T20:42:07.196Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "183.482ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 183.482ms 20041d1a-1c3d-4c1b-86e3-2c4f265aa5cc
[hub] 2025-07-17T20:42:07.381Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "185.063ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 185.063ms f636564b-037f-4605-bebe-903fbb7b535f
[hub] 2025-07-17T20:42:07.682Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "300.29ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:07.683Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "300.469ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 300.469ms 2b6b4fef-8e5f-4da7-a3e1-63838071666e
[hub] 2025-07-17T20:42:07.857Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "173.068ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 173.068ms 3b552448-cfaa-47cb-a777-7e17429c273e
[hub] 2025-07-17T20:42:08.017Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "159.447ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 159.447ms 83780a4b-6783-40a5-9114-8d5c8e40821f
[hub] 2025-07-17T20:42:08.174Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "156.297ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 156.297ms 957ff45b-9ca2-4b2e-acba-0bca9fb4598f
[hub] 2025-07-17T20:42:08.394Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "218.86ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:08.394Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "219.05ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 219.050ms 36fbd221-ce68-4e29-8975-f83e7d0673cc
[hub] 2025-07-17T20:42:08.569Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "173.538ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 173.538ms e4333841-2ee7-4c9d-b362-9586f4cf6ca6
[hub] 2025-07-17T20:42:08.748Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "178.863ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 178.863ms fbafaf98-9516-4de9-b73d-9f4014911c79
[hub] 2025-07-17T20:42:08.969Z [WARN] Slow API request {
[hub]   "method": "GET",
[hub]   "path": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "duration": "219.74ms",
[hub]   "queryCount": 0,
[hub]   "queryTime": "0.00ms",
[hub]   "statusCode": 200
[hub] }
[hub] 2025-07-17T20:42:08.969Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "219.933ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 219.933ms 1091f109-91db-403b-b263-0a3ed7841730
[hub] 2025-07-17T20:42:09.148Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "177.781ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 177.781ms 42c55989-62af-4699-a9f9-1159f7c3f0cc
[hub] 2025-07-17T20:42:09.331Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "182.865ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 182.865ms 6f6c9be4-62bb-4294-85b0-d55d19c7f227
[hub] 2025-07-17T20:42:09.490Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "158.055ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 158.055ms 6f3d4c42-72d3-4d20-a141-013344a9f591
[hub] 2025-07-17T20:42:09.664Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "173.048ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 173.048ms f7e03ac2-6d86-4421-813a-6f0bf9903193
[hub] 2025-07-17T20:42:09.827Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "161.738ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 161.738ms 1cc0496e-d172-4088-b947-74bcc9d1f604
[hub] 2025-07-17T20:42:09.979Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "151.144ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 151.144ms 68061e79-4d13-4659-93c7-aff8861d2a51
[hub] 2025-07-17T20:42:10.139Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "159.042ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 159.042ms e371b05a-f63b-440c-8ab7-714ee11684c1
[hub] 2025-07-17T20:42:10.304Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "164.602ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 164.602ms 35ec360b-3438-442a-b2ed-d50aa624f239
[hub] 2025-07-17T20:42:10.475Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "169.291ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 169.291ms 9237afaa-a86e-4a7c-ba31-ef1ac38679b5
[hub] 2025-07-17T20:42:10.650Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "174.117ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 174.117ms 0221589a-3a80-463a-95a6-8f315ad480f8
[hub] 2025-07-17T20:42:10.807Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "155.559ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 155.559ms d2c7e656-c102-4515-9dba-40b09136d60d
[hub] 2025-07-17T20:42:10.972Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "164.218ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 164.218ms b965dd0e-a014-419d-98ea-5c69e2020c60
[hub] 2025-07-17T20:42:11.161Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "188.174ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 188.174ms f9968c31-c2c7-43ed-b780-5868ec6c86df
[hub] 2025-07-17T20:42:11.333Z [INFO] HTTP Request {
[hub]   "method": "GET",
[hub]   "url": "/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd",
[hub]   "statusCode": 304,
[hub]   "responseTime": "171.108ms",
[hub]   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
[hub]   "ip": "::1"
[hub] }
[hub] GET /api/v1/import/status/abdeb30c-9a10-4bb7-b130-a5fb0e3aa5bd 304 171.108ms 4832df4c-8f57-4ff4-a80c-4025869042ed
[hub] 2025-07-17T20:42:11.355Z [INFO] Shutting down Redis connection pool 


Shutting down services...
[hub] Process exited with code null
Terminate batch job (Y/N)? 
PS C:\Users\<USER>\Travelviz\Travelviz> 
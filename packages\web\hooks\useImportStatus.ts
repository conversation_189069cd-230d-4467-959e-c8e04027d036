'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { importApi, type ParseStatusResponse } from '@/lib/api/import';

export interface UseImportStatusOptions {
  importId: string | null;
  importType?: 'text' | 'pdf';
  pollingInterval?: number;
  maxPollingTime?: number;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  onProgress?: (progress: number, step: string) => void;
}

export interface ImportStatusState {
  status: 'pending' | 'processing' | 'complete' | 'error' | null;
  progress: number;
  currentStep: string;
  result: any;
  error: string | null;
  isPolling: boolean;
  timeElapsed: number;
  retryCount: number;
}

const DEFAULT_POLLING_INTERVAL = 5000; // 5 seconds - reduced frequency to avoid overwhelming backend
const DEFAULT_MAX_POLLING_TIME = 300000; // 5 minutes - increased for long-running AI operations
const MAX_RETRIES = 3;
const RETRY_DELAY = 5000; // 5 seconds

export function useImportStatus(options: UseImportStatusOptions): ImportStatusState & {
  startPolling: () => void;
  stopPolling: () => void;
  retry: () => void;
} {
  const {
    importId,
    importType = 'text',
    pollingInterval = DEFAULT_POLLING_INTERVAL,
    maxPollingTime = DEFAULT_MAX_POLLING_TIME,
    onComplete,
    onError,
    onProgress
  } = options;

  const [state, setState] = useState<ImportStatusState>({
    status: null,
    progress: 0,
    currentStep: 'initializing',
    result: null,
    error: null,
    isPolling: false,
    timeElapsed: 0,
    retryCount: 0
  });

  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const timeElapsedRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(Date.now());
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update time elapsed
  useEffect(() => {
    if (state.isPolling) {
      timeElapsedRef.current = setInterval(() => {
        setState(prev => ({
          ...prev,
          timeElapsed: Date.now() - startTimeRef.current
        }));
      }, 1000);
    } else {
      if (timeElapsedRef.current) {
        clearInterval(timeElapsedRef.current);
        timeElapsedRef.current = null;
      }
    }

    return () => {
      if (timeElapsedRef.current) {
        clearInterval(timeElapsedRef.current);
      }
    };
  }, [state.isPolling]);

  const stopPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
    setState(prev => ({ ...prev, isPolling: false }));
  }, []);

  const checkStatus = useCallback(async () => {
    if (!importId) return;

    try {
      const statusResponse = await importApi.getParseStatus(importId, importType);

      setState(prev => ({
        ...prev,
        status: statusResponse.status as any,
        progress: statusResponse.progress || 0,
        currentStep: statusResponse.status === 'processing' ? 'parsing' : statusResponse.status,
        result: statusResponse.result,
        error: statusResponse.error || null
      }));

      // Call progress callback
      if (onProgress) {
        onProgress(statusResponse.progress || 0, statusResponse.status === 'processing' ? 'parsing' : statusResponse.status);
      }

      // Handle completion
      if (statusResponse.status === 'completed') {
        stopPolling();
        if (statusResponse.result && onComplete) {
          onComplete(statusResponse.result);
        }
        return;
      }

      // Handle errors
      if (statusResponse.status === 'failed') {
        stopPolling();
        const errorMessage = statusResponse.error || 'Parsing failed. Please try again.';
        setState(prev => ({ ...prev, error: errorMessage }));
        if (onError) {
          onError(errorMessage);
        }
        return;
      }

      // Reset retry count on successful status check
      setState(prev => ({ ...prev, retryCount: 0 }));

    } catch (error) {
      console.error('Error checking import status:', error);

      // Check if this is a timeout error vs other network error
      const isTimeoutError = error instanceof Error &&
        (error.message.includes('timeout') || error.message.includes('aborted'));

      // Handle network errors with retry logic
      setState(prev => {
        const newRetryCount = prev.retryCount + 1;

        if (newRetryCount >= MAX_RETRIES) {
          stopPolling();
          const errorMessage = isTimeoutError
            ? 'Request timed out. The AI processing is taking longer than expected. Please try again.'
            : 'Failed to check import status. Please try again.';
          if (onError) {
            onError(errorMessage);
          }
          return {
            ...prev,
            error: errorMessage,
            retryCount: newRetryCount
          };
        }

        // Schedule retry - use polling interval instead of immediate retry
        retryTimeoutRef.current = setTimeout(() => {
          checkStatus();
        }, pollingInterval); // Use pollingInterval instead of RETRY_DELAY to maintain consistent polling

        return {
          ...prev,
          retryCount: newRetryCount
        };
      });
    }
  }, [importId, onComplete, onError, onProgress, stopPolling]);

  const startPolling = useCallback(() => {
    if (!importId) return;

    // Reset state
    startTimeRef.current = Date.now();
    setState(prev => ({
      ...prev,
      isPolling: true,
      error: null,
      retryCount: 0,
      timeElapsed: 0
    }));

    // Initial check
    checkStatus();

    // Start polling
    pollingRef.current = setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current;
      
      if (elapsed > maxPollingTime) {
        stopPolling();
        const timeoutError = 'Import is taking longer than expected. Please try again or contact support.';
        setState(prev => ({ ...prev, error: timeoutError }));
        if (onError) {
          onError(timeoutError);
        }
        return;
      }

      checkStatus();
    }, pollingInterval);
  }, [importId, maxPollingTime, pollingInterval, checkStatus, stopPolling, onError]);

  const retry = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
      retryCount: 0,
      status: null,
      progress: 0,
      currentStep: 'initializing'
    }));
    startPolling();
  }, [startPolling]);

  // Auto-start polling when importId changes
  useEffect(() => {
    if (importId) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [importId, startPolling, stopPolling]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    ...state,
    startPolling,
    stopPolling,
    retry
  };
}
